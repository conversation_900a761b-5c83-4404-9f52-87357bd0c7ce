#pragma once

#define USE_UNIQUE_COMPONENT_ID
#define USE_UNIQUE_SERIALIZE_ID

#include <stdint.h>

constexpr uint32_t ADORECORDSET_UNICODE_BUFFER_SIZE = 65536;	//ADORECORDSET UNICODE 변환버퍼 사이즈

// Probability
constexpr int PERCENT_MAX = 100;
constexpr int PERMILLE_MAX = 1000;
constexpr int BASISPOINT_MAX = 10000;
constexpr int PROBABILITY_REWARD = BASISPOINT_MAX;
constexpr int MULTIPLIER_QUEST_BONUS = BASISPOINT_MAX;

// Redis
constexpr int DOMAIN_LENGTH = 100;
constexpr int PASSWORD_LENGTH = 64;
constexpr int LOCK_THREAD_COUNT = 99;

//PartyManager
constexpr uint64_t PARTY_MANAGER_UPDATE_TICK = 1000;			//파티매니져 업데이트 Tick
//constexpr uint64_t AUTO_MAP_MODULE_DESTORY_TICK = 10000;		//10초후에 인던맵은 삭제된다.
constexpr uint64_t AUTO_MAP_MODULE_DESTORY_TICK = 120000;		// 모듈 삭제 시간(2분)
constexpr uint64_t MAP_MODULE_IDLE_TIMEOUT_TICK = 180000;		// 맵 모듈 유휴 시간 초과(3분)

// 리전당 채널
constexpr auto CHANNEL_MAX = 10;
constexpr auto CHANNEL_ID_MIN = 1;
constexpr auto CHANNEL_ID_MAX = CHANNEL_ID_MIN + CHANNEL_MAX;

//////////////////////////////////////////////////////////////////////////
// Define 문

#if !defined(IN)
#define IN
#endif
#if !defined(OUT)
#define OUT
#endif
#if !defined(INOUT)
#define INOUT
#endif

#if defined(_DEBUG)

//#define CHECK_MEM_ALLOC_STATE // 메모리 할당 상태 체크

#if defined(CHECK_MEM_ALLOC_STATE)

#include <crtdbg.h>

#define _CRTDBG_MAP_ALLOC

#define DEBUG_NEW new (_NORMAL_BLOCK, __FILE__, __LINE__)
#define DEBUG_MALLOC(size) _malloc_dbg(size, _NORMAL_BLOCK, __FILE__, __LINE__)
#define new DEBUG_NEW
#define malloc DEBUG_MALLOC

#endif // defined(CHECK_MEM_ALLOC_STATE)

#endif // defined(_DEBUG)

//#define CELL_MOVE_THRESHOLD