#pragma once
#include <functional>
#include <thread>
#include <chrono>
#include <utility>
#include "NSDefine.h"

class RetryPolicy
{
public:
    // 재시도 가능한 MySQL 에러 코드
    enum MySQLError
    {
        DEADLOCK = 1213,                    // Deadlock found when trying to get lock
        LOCK_WAIT_TIMEOUT = 1205,           // Lock wait timeout exceeded
        SERVER_GONE_AWAY = 2006,            // MySQL server has gone away
        LOST_CONNECTION = 2013,             // Lost connection to MySQL server
        SERVER_SHUTDOWN = 1053,             // Server shutdown in progress
        CONNECTION_KILLED = 1317,           // Query execution was interrupted
        TABLE_LOCKED = 1099,                // Table is locked
        LOCK_ABORTED = 1689,                // Wait on a lock was aborted
        LOCK_DEADLOCK = 3572,               // Statement was aborted by a lock wait timeout or deadlock
    };

    // 재시도 설정
    struct Config
    {
        int maxRetries = 3;                                         // 최대 재시도 횟수
        std::chrono::milliseconds initialDelay{100};                // 초기 지연 시간
        std::chrono::milliseconds maxDelay{5000};                   // 최대 지연 시간
        double backoffMultiplier = 2.0;                             // 지수 백오프 배수
        bool enableJitter = true;                                   // 지터 활성화 여부
    };

    // 재시도 결과
    template<typename T>
    struct Result
    {
        T value;
        EErrorCode errorCode;
        int mysqlError;
        int attempts;
        std::chrono::milliseconds totalDelay;
    };

    // 기본 재시도 실행 (반환값이 있는 경우)
    template<typename Func, typename... Args>
    static auto ExecuteWithRetry(const Config& config, Func func, Args&&... args)
        -> Result<decltype(func(std::forward<Args>(args)...))>
    {
        using ReturnType = decltype(func(std::forward<Args>(args)...));
        Result<ReturnType> result{};
        
        std::chrono::milliseconds currentDelay = config.initialDelay;
        
        for (int attempt = 0; attempt < config.maxRetries; ++attempt)
        {
            result.attempts = attempt + 1;
            
            try
            {
                auto [value, errorCode, mysqlError] = func(std::forward<Args>(args)...);
                result.value = value;
                result.errorCode = errorCode;
                result.mysqlError = mysqlError;
                
                if (errorCode == EErrorCode::None || !IsRetryableError(mysqlError))
                {
                    return result;
                }
                
                // 마지막 시도가 아니면 재시도
                if (attempt < config.maxRetries - 1)
                {
                    auto delay = CalculateDelay(currentDelay, config);
                    result.totalDelay += delay;
                    std::this_thread::sleep_for(delay);
                    currentDelay = std::min(
                        std::chrono::milliseconds(static_cast<int64_t>(currentDelay.count() * config.backoffMultiplier)),
                        config.maxDelay
                    );
                }
            }
            catch (...)
            {
                result.errorCode = EErrorCode::UnknownError;
                result.mysqlError = 0;
                
                if (attempt < config.maxRetries - 1)
                {
                    auto delay = CalculateDelay(currentDelay, config);
                    result.totalDelay += delay;
                    std::this_thread::sleep_for(delay);
                    currentDelay = std::min(
                        std::chrono::milliseconds(static_cast<int64_t>(currentDelay.count() * config.backoffMultiplier)),
                        config.maxDelay
                    );
                }
            }
        }
        
        // 모든 재시도 실패
        if (result.errorCode == EErrorCode::None)
        {
            result.errorCode = EErrorCode::MaxRetriesExceeded;
        }
        
        return result;
    }

    // 간단한 재시도 실행 (기본 설정 사용)
    template<typename Func, typename... Args>
    static auto ExecuteWithRetry(Func func, Args&&... args)
        -> Result<decltype(func(std::forward<Args>(args)...))>
    {
        return ExecuteWithRetry(Config{}, func, std::forward<Args>(args)...);
    }

    // 재시도 가능한 에러인지 확인
    static bool IsRetryableError(int mysqlError)
    {
        switch (mysqlError)
        {
            case DEADLOCK:
            case LOCK_WAIT_TIMEOUT:
            case TABLE_LOCKED:
            case LOCK_ABORTED:
            case LOCK_DEADLOCK:
                return true;
                
            case SERVER_GONE_AWAY:
            case LOST_CONNECTION:
            case SERVER_SHUTDOWN:
            case CONNECTION_KILLED:
                // 연결 관련 에러는 재연결이 필요하므로 별도 처리 필요
                return false;
                
            default:
                return false;
        }
    }

    // 연결 관련 에러인지 확인
    static bool IsConnectionError(int mysqlError)
    {
        switch (mysqlError)
        {
            case SERVER_GONE_AWAY:
            case LOST_CONNECTION:
            case SERVER_SHUTDOWN:
            case CONNECTION_KILLED:
                return true;
                
            default:
                return false;
        }
    }

private:
    // 지연 시간 계산 (지터 포함)
    static std::chrono::milliseconds CalculateDelay(
        std::chrono::milliseconds baseDelay, 
        const Config& config)
    {
        if (!config.enableJitter)
        {
            return baseDelay;
        }
        
        // 0.5 ~ 1.5 범위의 랜덤 지터 적용
        double jitter = 0.5 + (static_cast<double>(rand()) / RAND_MAX);
        return std::chrono::milliseconds(
            static_cast<int64_t>(baseDelay.count() * jitter)
        );
    }
};